using UnityEditor;
using UnityEngine;

namespace Soup
{
    public class SoupSettingsProvider : SettingsProvider
    {
        public static bool DebugMode
        {
            get => EditorPrefs.GetBool("Soup.DebugMode", false);
            private set => EditorPrefs.SetBool("Soup.DebugMode", value);
        }

        private SoupSettingsProvider(string path, SettingsScope scope) : base(path, scope)
        {
        }

        public override void OnGUI(string searchContext)
        {
            base.OnGUI(searchContext);
            GUILayout.Space(10);

            GUILayout.Label("General", EditorStyles.boldLabel);
            DebugMode = EditorGUILayout.Toggle("Debug Mode", DebugMode);
        }

        [SettingsProvider]
        public static SettingsProvider CreateSoupSettingsProvider()
        {
            return new SoupSettingsProvider("Preferences/Soup", SettingsScope.User);
        }
    }
}