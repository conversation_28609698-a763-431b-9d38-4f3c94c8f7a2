using System;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;

namespace Soup
{
    public enum AssetType
    {
        Value,
        Collection,
        Event
    }

    public class GeneratorWindow : EditorWindow
    {
        private static string ShouldCreateAssetKey => "ShouldCreateAsset";

        private AssetType _assetType = AssetType.Value;
        private string _typeName;
        private string _typeNamespace = "Soup";
        private string _savePath = Path.Combine(Application.dataPath, "Generated");

        private bool _createAsset;
        private string _assetName;
        private string _assetSavePath = Path.Combine(Application.dataPath);

        [MenuItem("Soup/Generator")]
        private static void ShowWindow()
        {
            var window = GetWindow<GeneratorWindow>();
            window.titleContent = new GUIContent("Soup Generator");
            window.Show();
        }

        private void OnGUI()
        {
            _assetType = (AssetType)EditorGUILayout.EnumPopup("Asset Type", _assetType);
            _typeName = EditorGUILayout.TextField("Type Name", _typeName);
            _typeNamespace = EditorGUILayout.TextField("Type Namespace", _typeNamespace);

            GUILayout.BeginHorizontal();
            _savePath = EditorGUILayout.TextField("Path", _savePath);

            if (GUILayout.Button("Browse", GUILayout.Width(70)))
            {
                string newPath = EditorUtility.OpenFolderPanel("Select Save Folder", _savePath, "");
                _savePath = !string.IsNullOrEmpty(newPath) ? newPath : Application.dataPath;
            }

            GUILayout.EndHorizontal();
            GUILayout.Space(10);

            _createAsset = EditorGUILayout.Toggle("Create Asset", _createAsset);

            if (_createAsset)
            {
                _assetName = EditorGUILayout.TextField("Asset Name", _assetName);

                GUILayout.BeginHorizontal();
                _assetSavePath = EditorGUILayout.TextField("Asset Save Path", _assetSavePath);

                if (GUILayout.Button("Browse", GUILayout.Width(70)))
                {
                    string newPath = EditorUtility.OpenFolderPanel("Select Save Folder", _assetSavePath, "");
                    _assetSavePath = !string.IsNullOrEmpty(newPath) ? newPath : Application.dataPath;
                }

                GUILayout.EndHorizontal();
            }

            GUILayout.Space(10);

            if (GUILayout.Button("Generate", GUILayout.Height(30)) && !string.IsNullOrEmpty(_typeName))
            {
                Generate(_assetType, _typeName, _savePath, _typeNamespace);

                if (_createAsset)
                    EditorPrefs.SetBool(ShouldCreateAssetKey, true);
                else
                    GetWindow<GeneratorWindow>().Close();
            }
        }

        private static void Generate(
            AssetType assetTypeSelection,
            string typeName,
            string saveDir,
            string namespaceName)
        {
            try
            {
                var displayTypeName = ToPascalCase(typeName.Trim());
                namespaceName = namespaceName.Trim();

                (string, string)[] templates = assetTypeSelection switch
                {
                    AssetType.Value => GetValueTemplates(typeName, displayTypeName, namespaceName),
                    AssetType.Collection => GetCollectionTemplates(typeName, displayTypeName, namespaceName),
                    AssetType.Event => GetEventTemplates(typeName, displayTypeName, namespaceName),
                    _ => throw new ArgumentOutOfRangeException(nameof(assetTypeSelection), assetTypeSelection, null)
                };

                foreach (var (fileName, fileContent) in templates)
                {
                    string savePath = Path.Combine(saveDir, fileName);
                    Directory.CreateDirectory(saveDir);
                    File.WriteAllText(savePath, fileContent);
                }

                AssetDatabase.Refresh();

                EditorLogger.Log(
                    $"Created '{assetTypeSelection}' for type '{namespaceName}.{typeName}' in '{saveDir}'");
            }
            catch (Exception e)
            {
                EditorLogger.LogError(
                    $"Failed to generate '{assetTypeSelection}' for type '{namespaceName}.{typeName}' " +
                    $"in '{saveDir}': {e.Message}");
            }
        }

        [UnityEditor.Callbacks.DidReloadScripts]
        private static void OnScriptsReloaded()
        {
            if (!EditorPrefs.GetBool(ShouldCreateAssetKey, false))
                return;

            var window = GetWindow<GeneratorWindow>();

            try
            {
                var assetType = window._assetType;
                var assetSavePath = GetRelativePath(window._assetSavePath);
                var assetName = window._assetName;
                var typeName = window._typeName;
                var namespaceName = window._typeNamespace;

                var displayTypeName = ToPascalCase(typeName.Trim());
                var completeTypeName = GetTypeName(assetType, displayTypeName, namespaceName);

                Type type = FindType(completeTypeName);

                if (type != null)
                {
                    CreateAsset(type, assetSavePath, assetName);

                    EditorLogger.Log($"Created '{assetType}' asset for '{displayTypeName}'");
                }
                else
                {
                    throw new Exception($"Could not find type {completeTypeName}");
                }
            }
            catch (Exception e)
            {
                EditorLogger.LogError($"Failed to create asset: {e.Message}");
            }
            finally
            {
                EditorPrefs.DeleteKey(ShouldCreateAssetKey);
                window?.Close();
            }
        }

        private static string GetTypeName(AssetType assetType, string displayTypeName, string namespaceName)
        {
            var suffix = assetType switch
            {
                AssetType.Value => "Store",
                AssetType.Collection => "Collection",
                AssetType.Event => "Event",
                _ => throw new ArgumentOutOfRangeException(nameof(assetType), assetType, null)
            };
            var typeName = $"{namespaceName}.{displayTypeName}{suffix}";
            return typeName;
        }

        private static void CreateAsset(Type type, string assetSavePath, string assetName)
        {
            ScriptableObject asset = CreateInstance(type);
            AssetDatabase.CreateAsset(asset, $"{assetSavePath}/{assetName}.asset");
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
        }

        private static (string, string)[] GetEventTemplates(
            string typeName,
            string displayName,
            string namespaceName)
        {
            return new[]
            {
                ($"{displayName}Event.cs", $@"using UnityEngine;

namespace {namespaceName}
{{
    [CreateAssetMenu(menuName = ""Soup/Events/{displayName}"", fileName = ""{displayName} Event"")]
    public class {displayName}Event : GameEvent<{typeName}>
    {{
    }}
}}"),
                ($"{displayName}Listener.cs", @$"namespace {namespaceName}
{{
    public class {displayName}Listener : GameEventListener<{displayName}Event, {typeName}>
    {{
    }}
}}")
            };
        }

        private static (string, string)[] GetCollectionTemplates(
            string typeName,
            string displayName,
            string namespaceName)
        {
            return new[]
            {
                ($"{displayName}Collection.cs", @$"using UnityEngine;

namespace {namespaceName}
{{
    [CreateAssetMenu(menuName = ""Soup/Collections/{displayName}"", fileName = ""{displayName} Collection"")]
    public class {displayName}Collection : CollectionStore<{typeName}>
    {{
    }}
}}")
            };
        }

        private static (string, string)[] GetValueTemplates(
            string typeName,
            string displayName,
            string namespaceName)
        {
            return new[]
            {
                ($"{displayName}Value.cs", @$"using UnityEngine;

namespace {namespaceName}
{{
    [CreateAssetMenu(menuName = ""Soup/Values/{displayName}"", fileName = ""{displayName} Value"")]
    public class {displayName}Store : ValueStore<{typeName}>
    {{
    }}
}}")
            };
        }

        private static string GetRelativePath(string path)
        {
            int index = path.IndexOf("Assets/", StringComparison.Ordinal);

            if (index == -1)
            {
                return "Assets";
            }

            return path[index..].Replace("//", "/");
        }

        private static string ToPascalCase(string str)
        {
            if (string.IsNullOrEmpty(str))
                return str;
            return Regex.Replace(str, "(^|_)([a-z])", m => m.Groups[2].Value.ToUpper());
        }

        private static Type FindType(string typeName)
        {
            return AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .FirstOrDefault(t => t.FullName == typeName);
        }
    }
}