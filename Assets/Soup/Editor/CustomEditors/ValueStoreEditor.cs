using System.Reflection;
using UnityEditor;
using UnityEngine;

namespace Soup
{
    [CustomEditor(typeof(ValueStore<>), true)]
    public class ValueStoreEditor : EventRegistryDrawer
    {
        private void OnEnable()
        {
            _eventRegistryField = target.GetType().BaseType
                ?.GetField("_eventRegistry", BindingFlags.Instance | BindingFlags.NonPublic);
        }

        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            DrawListeners();
        }
    }
}
