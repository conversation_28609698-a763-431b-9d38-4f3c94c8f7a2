using System;
using System.IO;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Soup
{
    [InitializeOnLoad]
    public static class EditorLogger
    {
        static EditorLogger()
        {
            if (!SessionState.GetBool("FirstInitDone", false))
            {
                CleanupLogFile();
                SessionState.SetBool("FirstInitDone", true);
            }
        }

        private static string LogDirectory => Path.Combine(Directory.GetCurrentDirectory(), "Logs");
        private static string LogFilePath => Path.Combine(LogDirectory, "soup-editor.log");

        public static void Log(string message, Object context = null) =>
            InternalLog(message, context, Debug.Log, false);

        public static void LogWarning(string message, Object context = null) =>
            InternalLog(message, context, Debug.LogWarning);

        public static void LogError(string message, Object context = null) =>
            InternalLog(message, context, Debug.LogError);

        private static void InternalLog(
            string message,
            Object context,
            Action<string, Object> action,
            bool writeConsole = true,
            bool writeFile = true)
        {
            if (!SoupSettingsProvider.DebugMode) return;

            if (writeConsole)
            {
                string decorated = $"Soup: {message}";
                action?.Invoke(decorated, context);
            }

            if (writeFile)
            {
                string timestamped = $"[{DateTime.Now}] {message}";
                TextWriter writer = new StreamWriter(LogFilePath, true);
                writer.WriteLine(timestamped);
                writer.Close();
            }
        }
        
        private static void CleanupLogFile()
        {
            if (File.Exists(LogFilePath))
            {
                File.Delete(LogFilePath);
            }
        }
    }
}