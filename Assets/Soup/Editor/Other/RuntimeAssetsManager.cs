using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Object = UnityEngine.Object;

namespace Soup
{
    [InitializeOnLoad]
    public static class RuntimeAssetsManager
    {
        static RuntimeAssetsManager()
        {
            EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
        }

        [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
        private static void Init()
        {
            foreach (var receiver in GetReceivers())
            {
                receiver.OnWillEnterPlayMode();
            }
        }

        private static void OnPlayModeStateChanged(PlayModeStateChange state)
        {
            if (state != PlayModeStateChange.ExitingPlayMode) return;

            foreach (var receiver in GetReceivers())
            {
                receiver.OnExitingPlayMode();
            }
        }

        private static IEnumerable<IPlayModeStateChangeReceiver> GetReceivers()
        {
            var typesToReset = TypeCache.GetTypesDerivedFrom<IPlayModeStateChangeReceiver>();

            foreach (Type type in typesToReset)
            {
                if (type.IsAbstract || !type.IsSubclassOf(typeof(ScriptableObject)))
                    continue;

                // Making sure to get the assets with their Namespace included to avoid collisions
                string[] guids = AssetDatabase.FindAssets($"t:{type.FullName}");

                foreach (string guid in guids)
                {
                    var asset = AssetDatabase.LoadAssetAtPath<Object>(AssetDatabase.GUIDToAssetPath(guid));
                    IPlayModeStateChangeReceiver receiver;

                    try
                    {
                        receiver = (IPlayModeStateChangeReceiver)asset;
                    }
                    catch (Exception e)
                    {
                        Debug.LogError(
                            $"Failed to cast {asset.name} to IPlayModeStateChangeReceiver: {e.Message}", asset);
                        continue;
                    }

                    yield return receiver;
                }
            }
        }
    }
}