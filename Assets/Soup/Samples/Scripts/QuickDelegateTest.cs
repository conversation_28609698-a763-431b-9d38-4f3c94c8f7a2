using System.Linq;
using Soup;
using UnityEngine;

/// <summary>
/// Quick test to verify that the delegate type matching works correctly
/// in the AddListenerWithSelector method.
/// </summary>
public class QuickDelegateTest : MonoBehaviour
{
    [Header("Test Stores")]
    [SerializeField] private GpsCoordinateStore _gpsStore;
    [SerializeField] private Vector3Store _vector3Store;

    private void Start()
    {
        Debug.Log("=== Quick Delegate Test ===");
        
        // Initialize test data
        if (_gpsStore)
        {
            _gpsStore.Value = new GpsCoordinate { Latitude = 1.0f, Longitude = 2.0f };
        }
        
        if (_vector3Store)
        {
            _vector3Store.Value = new Vector3(1, 2, 3);
        }
        
        TestDelegateTypes();
    }

    private void TestDelegateTypes()
    {
        Debug.Log("Testing delegate type creation and matching...");

        if (_gpsStore)
        {
            try
            {
                // Test field access (GpsCoordinate.Latitude)
                System.Func<GpsCoordinate, float> selector = gps => gps.Latitude;
                System.Action<float> listener = lat => Debug.Log($"Latitude: {lat}");
                
                Debug.Log($"Selector type: {selector.GetType()}");
                Debug.Log($"Listener type: {listener.GetType()}");
                Debug.Log($"Selector generic args: [{string.Join(", ", selector.GetType().GetGenericArguments().Select(t => t.Name))}]");
                Debug.Log($"Listener generic args: [{string.Join(", ", listener.GetType().GetGenericArguments().Select(t => t.Name))}]");
                
                var subscription = _gpsStore.AddListener(selector, listener);
                Debug.Log("✅ GPS field delegate test PASSED");
                subscription.Dispose();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ GPS field delegate test FAILED: {ex.Message}");
            }
        }

        if (_vector3Store)
        {
            try
            {
                // Test property access (Vector3.x)
                System.Func<Vector3, float> selector = vec => vec.x;
                System.Action<float> listener = x => Debug.Log($"X: {x}");
                
                Debug.Log($"Vector3 Selector type: {selector.GetType()}");
                Debug.Log($"Vector3 Listener type: {listener.GetType()}");
                
                var subscription = _vector3Store.AddListener(selector, listener);
                Debug.Log("✅ Vector3 property delegate test PASSED");
                subscription.Dispose();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Vector3 property delegate test FAILED: {ex.Message}");
            }
        }
    }
}
