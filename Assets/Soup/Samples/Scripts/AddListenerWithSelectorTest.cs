using Soup;
using UnityEngine;

/// <summary>
/// Comprehensive test script to verify that the AddListenerWithSelector fix works correctly
/// for all the specific scenarios mentioned in the issue:
/// - GpsCoordinate.Latitude (float field)
/// - PlayerState.Level (int field) 
/// - Vector3.x (float property)
/// - Vector3.magnitude (float property)
/// </summary>
public class AddListenerWithSelectorTest : MonoBehaviour
{
    [Header("Test Value Stores")]
    [SerializeField] private GpsCoordinateStore _gpsStore;
    [SerializeField] private PlayerStateStore _playerStore;
    [SerializeField] private Vector3Store _vector3Store;

    [Header("Test Configuration")]
    [SerializeField] private bool _enableDetailedLogging = true;
    [SerializeField] private bool _runTestsOnStart = true;

    private void Start()
    {
        if (_runTestsOnStart)
        {
            Debug.Log("=== AddListenerWithSelector Fix Test ===");
            Debug.Log("Testing the fix for 'Invalid selector or listener types' error");
            
            InitializeTestData();
            RunAllTests();
        }
    }

    private void InitializeTestData()
    {
        LogTest("Initializing test data...");

        if (_gpsStore)
        {
            _gpsStore.Value = new GpsCoordinate 
            { 
                Latitude = 37.7749f,   // San Francisco
                Longitude = -122.4194f 
            };
            LogTest($"GPS initialized: {_gpsStore.Value}");
        }

        if (_playerStore)
        {
            _playerStore.Value = new PlayerState 
            { 
                Level = 5, 
                Position = new Vector3(10, 0, 5) 
            };
            LogTest($"Player initialized: Level={_playerStore.Value.Level}, Position={_playerStore.Value.Position}");
        }

        if (_vector3Store)
        {
            _vector3Store.Value = new Vector3(3f, 4f, 5f);
            LogTest($"Vector3 initialized: {_vector3Store.Value} (magnitude: {_vector3Store.Value.magnitude:F2})");
        }
    }

    private void RunAllTests()
    {
        LogTest("Starting AddListenerWithSelector tests...");

        // Test 1: GpsCoordinate.Latitude (float field)
        TestGpsLatitudeField();

        // Test 2: PlayerState.Level (int field)
        TestPlayerLevelField();

        // Test 3: Vector3.x (float property)
        TestVector3XProperty();

        // Test 4: Vector3.magnitude (float property)
        TestVector3MagnitudeProperty();

        LogTest("All AddListenerWithSelector tests completed!");
    }

    private void TestGpsLatitudeField()
    {
        if (!_gpsStore)
        {
            LogError("GpsCoordinateStore not assigned - skipping Latitude field test");
            return;
        }

        try
        {
            LogTest("Testing GpsCoordinate.Latitude (float field)...");

            // This should work without throwing "Invalid selector or listener types"
            var subscription = _gpsStore.AddListener(
                gps => gps.Latitude,  // Selector: GpsCoordinate -> float (field access)
                newLat => LogTest($"GPS Latitude changed to: {newLat:F4}")  // Listener: float -> void
            );

            // Test the subscription by changing the value
            var newGps = new GpsCoordinate { Latitude = 40.7128f, Longitude = _gpsStore.Value.Longitude };
            _gpsStore.Value = newGps;

            subscription.Dispose();
            LogTest("✅ GpsCoordinate.Latitude field test PASSED");
        }
        catch (System.Exception ex)
        {
            LogError($"❌ GpsCoordinate.Latitude field test FAILED: {ex.Message}");
        }
    }

    private void TestPlayerLevelField()
    {
        if (!_playerStore)
        {
            LogError("PlayerStateStore not assigned - skipping Level field test");
            return;
        }

        try
        {
            LogTest("Testing PlayerState.Level (int field)...");

            // This should work without throwing "Invalid selector or listener types"
            var subscription = _playerStore.AddListener(
                player => player.Level,  // Selector: PlayerState -> int (field access)
                newLevel => LogTest($"Player Level changed to: {newLevel}")  // Listener: int -> void
            );

            // Test the subscription by changing the value
            var newPlayer = new PlayerState { Level = 10, Position = _playerStore.Value.Position };
            _playerStore.Value = newPlayer;

            subscription.Dispose();
            LogTest("✅ PlayerState.Level field test PASSED");
        }
        catch (System.Exception ex)
        {
            LogError($"❌ PlayerState.Level field test FAILED: {ex.Message}");
        }
    }

    private void TestVector3XProperty()
    {
        if (!_vector3Store)
        {
            LogError("Vector3Store not assigned - skipping x property test");
            return;
        }

        try
        {
            LogTest("Testing Vector3.x (float property)...");

            // This should work without throwing "Invalid selector or listener types"
            var subscription = _vector3Store.AddListener(
                vec => vec.x,  // Selector: Vector3 -> float (property access)
                newX => LogTest($"Vector3.x changed to: {newX:F2}")  // Listener: float -> void
            );

            // Test the subscription by changing the value
            var currentVec = _vector3Store.Value;
            _vector3Store.Value = new Vector3(7f, currentVec.y, currentVec.z);

            subscription.Dispose();
            LogTest("✅ Vector3.x property test PASSED");
        }
        catch (System.Exception ex)
        {
            LogError($"❌ Vector3.x property test FAILED: {ex.Message}");
        }
    }

    private void TestVector3MagnitudeProperty()
    {
        if (!_vector3Store)
        {
            LogError("Vector3Store not assigned - skipping magnitude property test");
            return;
        }

        try
        {
            LogTest("Testing Vector3.magnitude (float property)...");

            // This should work without throwing "Invalid selector or listener types"
            var subscription = _vector3Store.AddListener(
                vec => vec.magnitude,  // Selector: Vector3 -> float (property access)
                newMag => LogTest($"Vector3.magnitude changed to: {newMag:F3}")  // Listener: float -> void
            );

            // Test the subscription by changing the value (this will change magnitude)
            _vector3Store.Value = new Vector3(6f, 8f, 0f); // magnitude = 10

            subscription.Dispose();
            LogTest("✅ Vector3.magnitude property test PASSED");
        }
        catch (System.Exception ex)
        {
            LogError($"❌ Vector3.magnitude property test FAILED: {ex.Message}");
        }
    }

    // Manual test methods for UI buttons
    public void TestGpsLatitudeManual()
    {
        TestGpsLatitudeField();
    }

    public void TestPlayerLevelManual()
    {
        TestPlayerLevelField();
    }

    public void TestVector3XManual()
    {
        TestVector3XProperty();
    }

    public void TestVector3MagnitudeManual()
    {
        TestVector3MagnitudeProperty();
    }

    public void RunAllTestsManual()
    {
        RunAllTests();
    }

    private void LogTest(string message)
    {
        if (_enableDetailedLogging)
        {
            Debug.Log($"[AddListenerTest] {message}");
        }
    }

    private void LogError(string message)
    {
        Debug.LogError($"[AddListenerTest] {message}");
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 350, 250));
        GUILayout.Label("AddListenerWithSelector Test", GUI.skin.box);
        
        if (GUILayout.Button("Run All Tests"))
        {
            RunAllTestsManual();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Individual Tests:", GUI.skin.box);
        
        if (GUILayout.Button("Test GPS Latitude (field)"))
        {
            TestGpsLatitudeManual();
        }
        
        if (GUILayout.Button("Test Player Level (field)"))
        {
            TestPlayerLevelManual();
        }
        
        if (GUILayout.Button("Test Vector3.x (property)"))
        {
            TestVector3XManual();
        }
        
        if (GUILayout.Button("Test Vector3.magnitude (property)"))
        {
            TestVector3MagnitudeManual();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Expected: All tests should PASS", GUI.skin.box);
        GUILayout.Label("If any test FAILS, check Console for details");
        
        GUILayout.EndArea();
    }
}
