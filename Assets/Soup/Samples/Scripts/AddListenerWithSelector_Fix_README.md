# AddListenerWithSelector Fix Documentation

## Issue Summary

The GenericPropertyBinding component was throwing the error:
```
"GenericPropertyBinding Error: Error subscribing to changes: Invalid selector or listener types"
```

This occurred when trying to subscribe to changes for both field and property bindings through the `AddListenerWithSelector` method.

## Root Cause Analysis

### The Problem
The original `AddListenerWithSelector` implementation in `ValueStore<T>` was trying to find the generic `AddListener<TSelection>` method using exact delegate types:

```csharp
// ❌ This approach was failing
var addListenerMethod = GetType().GetMethod("AddListener", new[] { selectorType, listenerType });
```

**Why it failed:**
1. **Generic Method Lookup**: The `AddListener<TSelection>` method is a generic method, so we can't find it by looking for exact concrete delegate types.
2. **Type Parameter Mismatch**: The reflection was looking for a method with specific `Func<T, TSelection>` and `Action<TSelection>` types, but the generic method definition has different type parameters.
3. **Missing Generic Instantiation**: Even if found, the method needed to be made concrete with the correct `TSelection` type.

### The Target Method
We needed to properly invoke this method:
```csharp
public EventSubscription AddListener<TSelection>(Func<T, TSelection> selector, Action<TSelection> listener)
```

## Solution Implementation

### 1. Enhanced Type Validation
Added comprehensive type checking with detailed error messages:

```csharp
var valueType = selectorType.GetGenericArguments()[0]; // T in Func<T, TSelection>
var selectionType = selectorType.GetGenericArguments()[1]; // TSelection in Func<T, TSelection>
var actionType = listenerType.GetGenericArguments()[0]; // TSelection in Action<TSelection>

// Verify that the selector and listener have matching selection types
if (selectionType != actionType) {
    throw new ArgumentException($"Selector return type ({selectionType.Name}) does not match listener parameter type ({actionType.Name})");
}

// Verify that the selector's input type matches our ValueStore's type T
if (valueType != typeof(T)) {
    throw new ArgumentException($"Selector input type ({valueType.Name}) does not match ValueStore type ({typeof(T).Name})");
}
```

### 2. Proper Generic Method Resolution
Fixed the method lookup to find the generic method definition and make it concrete:

```csharp
// Find the generic AddListener method definition
var addListenerMethods = GetType().GetMethods(BindingFlags.Public | BindingFlags.Instance)
    .Where(m => m.Name == "AddListener" && m.IsGenericMethodDefinition && m.GetParameters().Length == 2)
    .ToArray();

if (addListenerMethods.Length == 0) {
    throw new ArgumentException("Could not find generic AddListener method");
}

// Make the generic method concrete with the selection type
var genericMethod = addListenerMethods[0];
var concreteMethod = genericMethod.MakeGenericMethod(selectionType);

// Invoke the concrete method
return (EventSubscription)concreteMethod.Invoke(this, new object[] { selector, listener });
```

### 3. Improved Error Handling
Added comprehensive error handling with detailed debugging information:

```csharp
catch (Exception ex) {
    throw new ArgumentException($"Error in AddListenerWithSelector: {ex.Message}", ex);
}
```

## Test Scenarios Verified

### ✅ GpsCoordinate.Latitude (float field)
```csharp
// Selector: GpsCoordinate -> float (field access)
gps => gps.Latitude
// Listener: float -> void
newLat => Debug.Log($"Latitude: {newLat}")
```

### ✅ PlayerState.Level (int field)
```csharp
// Selector: PlayerState -> int (field access)
player => player.Level
// Listener: int -> void
newLevel => Debug.Log($"Level: {newLevel}")
```

### ✅ Vector3.x (float property)
```csharp
// Selector: Vector3 -> float (property access)
vec => vec.x
// Listener: float -> void
newX => Debug.Log($"X: {newX}")
```

### ✅ Vector3.magnitude (float property)
```csharp
// Selector: Vector3 -> float (property access)
vec => vec.magnitude
// Listener: float -> void
newMag => Debug.Log($"Magnitude: {newMag}")
```

## Type Safety Guarantees

The fix ensures:

1. **Selector Input Type Validation**: The selector's input type must match the ValueStore's type `T`
2. **Return Type Matching**: The selector's return type must match the listener's parameter type
3. **Generic Type Consistency**: All generic type parameters are properly validated and instantiated
4. **Runtime Type Safety**: Comprehensive error messages help identify type mismatches

## Performance Considerations

- **Reflection Overhead**: The method lookup and instantiation happens only once during subscription
- **Cached Execution**: Once the subscription is established, runtime performance is optimal
- **Error Prevention**: Early type validation prevents runtime errors during value changes

## Debugging Features

### Enhanced Error Messages
The fix provides detailed error messages for common issues:

```
"Selector return type (Single) does not match listener parameter type (Int32)"
"Selector input type (Vector2) does not match ValueStore type (Vector3)"
"Could not find generic AddListener method"
```

### Debug Information in GenericPropertyBinding
Added comprehensive debugging output when subscription fails:

```
GenericPropertyBinding Debug Info:
- ValueStore Type: GpsCoordinateStore
- Value Type: GpsCoordinate
- Member: field 'Latitude' of type Single
- Is Field: True
- Exception: [detailed exception info]
```

## Testing

### Automated Tests
- `AddListenerWithSelectorTest.cs` - Comprehensive test for all scenarios
- `QuickDelegateTest.cs` - Basic delegate type verification

### Manual Testing
- UI buttons for individual test scenarios
- Real-time validation in GenericPropertyBinding components
- Console logging for verification

## Migration Notes

This fix is **backward compatible** - no changes needed to existing code:

- ✅ Existing `AddListener(Action<T>)` calls continue to work
- ✅ Existing `AddListener<TSelection>(Func<T, TSelection>, Action<TSelection>)` calls continue to work
- ✅ New `AddListenerWithSelector(Delegate, Delegate)` calls now work correctly
- ✅ GenericPropertyBinding components will now work for both fields and properties

## Conclusion

The fix resolves the "Invalid selector or listener types" error by:

1. **Proper Generic Method Resolution**: Correctly finding and instantiating the generic `AddListener<TSelection>` method
2. **Type Safety**: Comprehensive validation of all type parameters
3. **Better Error Handling**: Detailed error messages for debugging
4. **Universal Compatibility**: Works with both fields and properties, all value types

The GenericPropertyBinding component now works seamlessly with any combination of ValueStore types and member access patterns.
