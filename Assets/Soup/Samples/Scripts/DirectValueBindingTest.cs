using Soup;
using UnityEngine;

/// <summary>
/// Comprehensive test script to verify that GenericPropertyBinding works correctly
/// with empty property names (direct value binding) for various ValueStore types.
/// </summary>
public class DirectValueBindingTest : MonoBehaviour
{
    [Header("Primitive Value Stores")]
    [SerializeField] private FloatStore _floatStore;
    [SerializeField] private StringStore _stringStore;
    [SerializeField] private IntStore _intStore;
    [SerializeField] private Vector3Store _vector3Store;

    [Header("Complex Value Stores")]
    [SerializeField] private GpsCoordinateStore _gpsStore;
    [SerializeField] private PlayerStateStore _playerStore;

    [Header("Test Configuration")]
    [SerializeField] private bool _enableAutoTest = true;
    [SerializeField] private float _testInterval = 2.5f;
    [SerializeField] private bool _logDetailedInfo = true;

    private float _lastTestTime;
    private int _testCounter = 0;

    private void Start()
    {
        InitializeTestData();
        
        if (_enableAutoTest)
        {
            Debug.Log("=== Direct Value Binding Test Started ===");
            Debug.Log("This test verifies that GenericPropertyBinding works with empty property names:");
            Debug.Log("- Primitive types: FloatStore, StringStore, IntStore → bind to entire value");
            Debug.Log("- Complex types: GpsCoordinateStore, PlayerStateStore → bind to entire object");
            Debug.Log($"Auto-testing every {_testInterval} seconds...");
        }
    }

    private void Update()
    {
        if (_enableAutoTest && Time.time - _lastTestTime >= _testInterval)
        {
            _lastTestTime = Time.time;
            _testCounter++;
            RunCyclicTest();
        }
    }

    private void InitializeTestData()
    {
        LogTest("Initializing test data for direct value binding...");

        // Initialize primitive types
        if (_floatStore)
        {
            _floatStore.Value = 42.5f;
            LogTest($"Float initialized: {_floatStore.Value}");
        }

        if (_stringStore)
        {
            _stringStore.Value = "Hello World";
            LogTest($"String initialized: '{_stringStore.Value}'");
        }

        if (_intStore)
        {
            _intStore.Value = 100;
            LogTest($"Int initialized: {_intStore.Value}");
        }

        if (_vector3Store)
        {
            _vector3Store.Value = new Vector3(1f, 2f, 3f);
            LogTest($"Vector3 initialized: {_vector3Store.Value}");
        }

        // Initialize complex types
        if (_gpsStore)
        {
            _gpsStore.Value = new GpsCoordinate 
            { 
                Latitude = 37.7749f, 
                Longitude = -122.4194f 
            };
            LogTest($"GPS initialized: {_gpsStore.Value}");
        }

        if (_playerStore)
        {
            _playerStore.Value = new PlayerState 
            { 
                Level = 5, 
                Position = new Vector3(10, 0, 5) 
            };
            LogTest($"Player initialized: Level={_playerStore.Value.Level}, Position={_playerStore.Value.Position}");
        }
    }

    private void RunCyclicTest()
    {
        Debug.Log($"=== Direct Value Binding Test Cycle {_testCounter} ===");
        
        switch (_testCounter % 6)
        {
            case 1:
                TestFloatDirectBinding();
                break;
            case 2:
                TestStringDirectBinding();
                break;
            case 3:
                TestIntDirectBinding();
                break;
            case 4:
                TestVector3DirectBinding();
                break;
            case 5:
                TestGpsDirectBinding();
                break;
            case 0:
                TestPlayerDirectBinding();
                break;
        }
    }

    private void TestFloatDirectBinding()
    {
        if (!_floatStore) return;

        float newValue = Random.Range(0f, 1000f);
        _floatStore.Value = newValue;
        LogTest($"FLOAT DIRECT BINDING: Updated to {newValue:F2} (should display entire float value)");
    }

    private void TestStringDirectBinding()
    {
        if (!_stringStore) return;

        string[] testStrings = { "Hello", "World", "Unity", "Binding", "Test", "Direct", "Value" };
        string newValue = testStrings[Random.Range(0, testStrings.Length)] + " " + Random.Range(1, 100);
        _stringStore.Value = newValue;
        LogTest($"STRING DIRECT BINDING: Updated to '{newValue}' (should display entire string value)");
    }

    private void TestIntDirectBinding()
    {
        if (!_intStore) return;

        int newValue = Random.Range(1, 1000);
        _intStore.Value = newValue;
        LogTest($"INT DIRECT BINDING: Updated to {newValue} (should display entire int value)");
    }

    private void TestVector3DirectBinding()
    {
        if (!_vector3Store) return;

        var newValue = new Vector3(
            Random.Range(-10f, 10f),
            Random.Range(-10f, 10f),
            Random.Range(-10f, 10f)
        );
        _vector3Store.Value = newValue;
        LogTest($"VECTOR3 DIRECT BINDING: Updated to {newValue} (should display entire Vector3 value)");
    }

    private void TestGpsDirectBinding()
    {
        if (!_gpsStore) return;

        var newValue = new GpsCoordinate
        {
            Latitude = Random.Range(-90f, 90f),
            Longitude = Random.Range(-180f, 180f)
        };
        _gpsStore.Value = newValue;
        LogTest($"GPS DIRECT BINDING: Updated to {newValue} (should display entire GpsCoordinate struct)");
    }

    private void TestPlayerDirectBinding()
    {
        if (!_playerStore) return;

        var newValue = new PlayerState
        {
            Level = Random.Range(1, 100),
            Position = new Vector3(
                Random.Range(-20f, 20f),
                Random.Range(0f, 10f),
                Random.Range(-20f, 20f)
            )
        };
        _playerStore.Value = newValue;
        LogTest($"PLAYER DIRECT BINDING: Updated to Level={newValue.Level}, Position={newValue.Position} (should display entire PlayerState object)");
    }

    // Manual test methods for UI buttons
    public void TestAllPrimitiveTypes()
    {
        Debug.Log("=== Manual Test: All Primitive Types ===");
        TestFloatDirectBinding();
        TestStringDirectBinding();
        TestIntDirectBinding();
        TestVector3DirectBinding();
    }

    public void TestAllComplexTypes()
    {
        Debug.Log("=== Manual Test: All Complex Types ===");
        TestGpsDirectBinding();
        TestPlayerDirectBinding();
    }

    public void TestSpecificFloat()
    {
        TestFloatDirectBinding();
    }

    public void TestSpecificString()
    {
        TestStringDirectBinding();
    }

    private void LogTest(string message)
    {
        if (_logDetailedInfo)
        {
            Debug.Log($"[DirectValueTest] {message}");
        }
    }

    private void OnGUI()
    {
        if (!_enableAutoTest) return;

        GUILayout.BeginArea(new Rect(10, 10, 450, 350));
        GUILayout.Label("Direct Value Binding Test", GUI.skin.box);
        
        GUILayout.Label($"Test Cycle: {_testCounter}");
        GUILayout.Label($"Next test in: {(_testInterval - (Time.time - _lastTestTime)):F1}s");
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Test All Primitive Types"))
        {
            TestAllPrimitiveTypes();
        }
        
        if (GUILayout.Button("Test All Complex Types"))
        {
            TestAllComplexTypes();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Individual Tests:", GUI.skin.box);
        
        if (GUILayout.Button("Test Float Direct Binding"))
        {
            TestSpecificFloat();
        }
        
        if (GUILayout.Button("Test String Direct Binding"))
        {
            TestSpecificString();
        }
        
        if (GUILayout.Button("Test GPS Direct Binding"))
        {
            TestGpsDirectBinding();
        }
        
        if (GUILayout.Button("Test Player Direct Binding"))
        {
            TestPlayerDirectBinding();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Setup Instructions:", GUI.skin.box);
        GUILayout.Label("1. Create GenericPropertyBinding components");
        GUILayout.Label("2. Assign ValueStores to them");
        GUILayout.Label("3. Leave Property Name field EMPTY");
        GUILayout.Label("4. Watch text update with entire values");
        
        GUILayout.Space(5);
        GUILayout.Label("Expected Behavior:", GUI.skin.box);
        GUILayout.Label("• Empty property name = bind to entire value");
        GUILayout.Label("• Primitives show the raw value");
        GUILayout.Label("• Complex types show ToString() output");
        
        GUILayout.EndArea();
    }
}
