# GenericPropertyBinding Component

## Overview

The `GenericPropertyBinding` component is a powerful, generic data binding solution that allows you to bind any property from any `ValueStore<T>` type to a TextMeshPro text component. This eliminates the need for creating specific binding components for each property type.

## Architecture Improvements

### 1. ValueStoreBase Class
- Added a non-generic base class `ValueStoreBase` that all `ValueStore<T>` classes inherit from
- Eliminates complex reflection-based validation code
- Provides type-safe access to ValueStore functionality without knowing the generic type parameter

### 2. Simplified Property Access
- Uses the new base class methods for cleaner, more reliable property access
- Improved error handling and validation
- Better performance due to reduced reflection overhead

## Features

- **Generic Type Support**: Works with any `ValueStore<T>` type
- **Property & Field Selection**: Specify any public property or field by name
- **Automatic Detection**: Automatically detects whether the member is a property or field
- **Format String Support**: Custom formatting for display values
- **Real-time Validation**: Editor validation with descriptive error messages
- **Type Safety**: Compile-time and runtime type checking
- **Performance Optimized**: Minimal reflection usage

## Usage Examples

### 1. GPS Coordinate Binding

```csharp
// Bind to Latitude property of GpsCoordinate
ValueStore: GpsCoordinateStore
Property Name: "Latitude"
Format String: "Lat: {0:F2}°"

// Bind to Longitude property of GpsCoordinate  
ValueStore: GpsCoordinateStore
Property Name: "Longitude"
Format String: "Lon: {0:F2}°"
```

### 2. Player State Binding

```csharp
// Bind to Level property of PlayerState
ValueStore: PlayerStateStore
Property Name: "Level"
Format String: "Level: {0}"

// Bind to Position property of PlayerState
ValueStore: PlayerStateStore
Property Name: "Position"
Format String: "Position: {0}"
```

### 3. Primitive Type Binding

```csharp
// For primitive types, you can bind to the value itself
// by leaving the property name empty or using a property like "magnitude" for Vector3
ValueStore: FloatStore
Property Name: "" (empty for direct value binding)
Format String: "{0:F1}"

ValueStore: Vector3Store
Property Name: "magnitude"
Format String: "Distance: {0:F2}"
```

## Setup Instructions

### 1. Create ValueStore Assets
1. Right-click in Project window
2. Navigate to Create > Soup > Values
3. Create the desired ValueStore type (GPS Coordinate, Player State, etc.)
4. Set initial values in the inspector

### 2. Setup UI Text Elements
1. Create a TextMeshPro text object in your scene
2. Add the `GenericPropertyBinding` component
3. Assign your ValueStore asset to the "Value Store" field
4. Enter the property name you want to bind to
5. Optionally customize the format string

### 3. Test the Binding
1. Add the `GenericPropertyBindingTest` component to a GameObject
2. Assign your ValueStore assets to the test script
3. Enable "Auto Test" to see automatic updates
4. Watch the text elements update as values change

## Property & Field Names Reference

### GpsCoordinate (Fields)
- `Latitude` (float field)
- `Longitude` (float field)

### PlayerState (Fields)
- `Level` (int field)
- `Position` (Vector3 field)

### Vector3 (Properties)
- `x`, `y`, `z` (float properties)
- `magnitude` (float property)
- `normalized` (Vector3 property)

### Vector2 (Properties)
- `x`, `y` (float properties)
- `magnitude` (float property)
- `normalized` (Vector2 property)

## Format String Examples

```csharp
"{0}"           // Default formatting
"{0:F2}"        // 2 decimal places
"{0:F0}"        // No decimal places
"Value: {0}"    // With label
"Lat: {0:F2}°"  // With units
"Level {0}/100" // With context
```

## Error Handling

The component provides descriptive error messages for common issues:

- "No ValueStore assigned" - Assign a ValueStore asset
- "No property name specified" - Enter a valid property or field name
- "Property or field 'X' not found on type Y" - Check member name spelling
- "Property 'X' is not readable" - Property must have a getter (fields are always readable)
- "ValueStore.Value is null" - Initialize the ValueStore value

## Performance Notes

- Property reflection is cached after validation
- Event subscriptions use the efficient selector-based listening
- Minimal overhead during runtime updates
- Editor validation only runs when values change

## Troubleshooting

### Text Not Updating
1. Check that the ValueStore asset is assigned
2. Verify the property name is spelled correctly
3. Ensure the ValueStore has a non-null value
4. Check the Console for error messages

### Property Not Found
1. Verify the property exists on the value type
2. Check that the property is public
3. Ensure the property has a getter
4. Use exact case-sensitive spelling

### Format Errors
1. Ensure format string is valid for the property type
2. Use appropriate format specifiers (F2 for floats, etc.)
3. Test format strings with simple values first

## Advanced Usage

### Custom ValueStore Types
The component works with any custom ValueStore types you create:

```csharp
[System.Serializable]
public struct CustomData
{
    public string Name;
    public int Score;
    public bool IsActive;
}

[CreateAssetMenu(menuName = "Soup/Values/Custom Data")]
public class CustomDataStore : ValueStore<CustomData>
{
}
```

Then bind to properties like "Name", "Score", or "IsActive".

### Multiple Bindings
You can have multiple GenericPropertyBinding components on different text elements, all bound to different properties of the same ValueStore, creating a complete UI dashboard.
