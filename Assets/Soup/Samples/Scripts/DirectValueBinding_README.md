# Direct Value Binding Feature

## Overview

The GenericPropertyBinding component now supports **Direct Value Binding** when the Property Name field is left empty. This allows you to bind directly to the entire ValueStore value instead of a specific property or field within that value.

## How It Works

### Empty Property Name = Direct Value Binding
When you leave the `Property Name` field empty (or null), the GenericPropertyBinding component automatically switches to **Direct Value Binding** mode:

- **Primitive Types**: Binds to the raw value (float, int, string, etc.)
- **Complex Types**: Binds to the entire object (displays via ToString())
- **Unity Types**: Binds to the complete structure (Vector3, Quaternion, etc.)

### Property Name Specified = Member Binding
When you specify a property name, the component works as before:
- **Fields**: Accesses public fields (e.g., GpsCoordinate.Latitude)
- **Properties**: Accesses public properties (e.g., Vector3.x)

## Usage Examples

### 1. Primitive Type Direct Binding

#### FloatStore Direct Binding
```csharp
ValueStore: FloatStore
Property Name: [EMPTY]           // ← Leave this empty!
Format String: "{0:F2}"          // Shows: "42.50"
```

#### StringStore Direct Binding
```csharp
ValueStore: StringStore
Property Name: [EMPTY]           // ← Leave this empty!
Format String: "Text: {0}"       // Shows: "Text: Hello World"
```

#### IntStore Direct Binding
```csharp
ValueStore: IntStore
Property Name: [EMPTY]           // ← Leave this empty!
Format String: "Count: {0}"      // Shows: "Count: 100"
```

### 2. Complex Type Direct Binding

#### GpsCoordinateStore Direct Binding
```csharp
ValueStore: GpsCoordinateStore
Property Name: [EMPTY]           // ← Leave this empty!
Format String: "GPS: {0}"        // Shows: "GPS: 37.77, -122.42"
```

#### PlayerStateStore Direct Binding
```csharp
ValueStore: PlayerStateStore
Property Name: [EMPTY]           // ← Leave this empty!
Format String: "Player: {0}"     // Shows: "Player: [PlayerState object]"
```

### 3. Unity Type Direct Binding

#### Vector3Store Direct Binding
```csharp
ValueStore: Vector3Store
Property Name: [EMPTY]           // ← Leave this empty!
Format String: "Position: {0}"   // Shows: "Position: (1.0, 2.0, 3.0)"
```

## Comparison: Direct vs Member Binding

### Direct Value Binding (Empty Property Name)
| ValueStore Type | Property Name | Result |
|----------------|---------------|---------|
| FloatStore | [EMPTY] | Displays the float value: `42.5` |
| StringStore | [EMPTY] | Displays the string: `"Hello World"` |
| GpsCoordinateStore | [EMPTY] | Displays entire struct: `"37.77, -122.42"` |
| Vector3Store | [EMPTY] | Displays entire vector: `"(1.0, 2.0, 3.0)"` |

### Member Binding (Specific Property Name)
| ValueStore Type | Property Name | Result |
|----------------|---------------|---------|
| GpsCoordinateStore | "Latitude" | Displays just latitude: `37.77` |
| GpsCoordinateStore | "Longitude" | Displays just longitude: `-122.42` |
| Vector3Store | "x" | Displays just x component: `1.0` |
| Vector3Store | "magnitude" | Displays vector length: `3.74` |

## Implementation Details

### Validation Logic
```csharp
// Empty property name = direct value binding
if (string.IsNullOrEmpty(_propertyName)) {
    _isDirectValueBinding = true;
    _isValidBinding = true;
    return;
}

// Non-empty property name = member binding (existing behavior)
// ... search for property or field
```

### Subscription Logic
```csharp
if (_isDirectValueBinding) {
    // Create identity selector: value => value
    ParameterExpression param = Expression.Parameter(valueType, "value");
    LambdaExpression lambda = Expression.Lambda(selectorType, param, param);
    Delegate selector = lambda.Compile();
} else {
    // Create member selector: value => value.PropertyName
    // ... existing member access logic
}
```

### Display Logic
```csharp
if (_isDirectValueBinding) {
    // Use the entire store value
    displayValue = storeValue;
} else {
    // Extract specific property or field
    displayValue = _isField 
        ? _fieldInfo.GetValue(storeValue)
        : _propertyInfo.GetValue(storeValue);
}
```

## Use Cases

### 1. Primitive Value Displays
Perfect for showing simple values without needing specific binding components:
- Health bars (FloatStore)
- Score displays (IntStore)
- Status messages (StringStore)
- Timer displays (FloatStore with time formatting)

### 2. Debug Information
Quickly display entire objects for debugging:
- Player state overview
- Configuration objects
- Coordinate information
- Any complex data structure

### 3. Simplified UI Setup
Reduce the number of binding components needed:
- One component per ValueStore instead of one per property
- Easier setup for simple displays
- Less configuration required

## Format String Tips

### Primitive Types
```csharp
"{0}"           // Raw value
"{0:F2}"        // 2 decimal places for floats
"{0:C}"         // Currency format
"{0:P}"         // Percentage format
"Value: {0}"    // With label
```

### Complex Types
```csharp
"{0}"           // Uses ToString() method
"Data: {0}"     // With label
"[{0}]"         // With brackets
```

### Custom ToString() Methods
For best results with complex types, implement custom ToString() methods:

```csharp
[System.Serializable]
public struct GpsCoordinate
{
    public float Latitude;
    public float Longitude;
    
    // Custom ToString for better display
    public override string ToString() => $"{Latitude:F4}, {Longitude:F4}";
}
```

## Testing

Use the `DirectValueBindingTest.cs` script to verify functionality:

1. **Setup**: Create GenericPropertyBinding components with empty Property Name fields
2. **Assign**: Connect various ValueStore types
3. **Test**: Run the test script to see automatic value updates
4. **Verify**: Check that entire values are displayed correctly

## Benefits

- ✅ **Simplified Setup**: No need to specify property names for simple bindings
- ✅ **Versatile**: Works with any ValueStore type
- ✅ **Backward Compatible**: Existing property/field bindings continue to work
- ✅ **Performance**: Efficient direct value access
- ✅ **Debugging**: Easy way to display entire objects
- ✅ **Flexible**: Can switch between direct and member binding as needed

## Migration

This feature is **completely backward compatible**:
- Existing bindings with property names continue to work unchanged
- New bindings can use empty property names for direct value access
- No code changes required for existing implementations
