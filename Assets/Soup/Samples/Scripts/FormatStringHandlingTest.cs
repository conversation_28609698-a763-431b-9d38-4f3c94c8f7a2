using Soup;
using UnityEngine;

/// <summary>
/// Comprehensive test script to verify that GenericPropertyBinding handles
/// null/empty format strings correctly by displaying raw values without formatting.
/// </summary>
public class FormatStringHandlingTest : MonoBehaviour
{
    [Header("Test Value Stores")]
    [SerializeField] private FloatStore _floatStore;
    [SerializeField] private StringStore _stringStore;
    [SerializeField] private IntStore _intStore;
    [SerializeField] private Vector3Store _vector3Store;
    [SerializeField] private GpsCoordinateStore _gpsStore;

    [Header("Test Configuration")]
    [SerializeField] private bool _enableAutoTest = true;
    [SerializeField] private float _testInterval = 3f;
    [SerializeField] private bool _logDetailedInfo = true;

    private float _lastTestTime;
    private int _testCounter = 0;

    private void Start()
    {
        InitializeTestData();
        
        if (_enableAutoTest)
        {
            Debug.Log("=== Format String Handling Test Started ===");
            Debug.Log("This test verifies that GenericPropertyBinding handles format strings correctly:");
            Debug.Log("- Empty/null format string → raw ToString() output");
            Debug.Log("- Specified format string → formatted output");
            Debug.Log("- Null values → displays 'null'");
            Debug.Log($"Auto-testing every {_testInterval} seconds...");
        }
    }

    private void Update()
    {
        if (_enableAutoTest && Time.time - _lastTestTime >= _testInterval)
        {
            _lastTestTime = Time.time;
            _testCounter++;
            RunCyclicTest();
        }
    }

    private void InitializeTestData()
    {
        LogTest("Initializing test data for format string handling...");

        if (_floatStore)
        {
            _floatStore.Value = 42.123456f;
            LogTest($"Float initialized: {_floatStore.Value}");
        }

        if (_stringStore)
        {
            _stringStore.Value = "Hello World";
            LogTest($"String initialized: '{_stringStore.Value}'");
        }

        if (_intStore)
        {
            _intStore.Value = 12345;
            LogTest($"Int initialized: {_intStore.Value}");
        }

        if (_vector3Store)
        {
            _vector3Store.Value = new Vector3(1.23456f, 2.34567f, 3.45678f);
            LogTest($"Vector3 initialized: {_vector3Store.Value}");
        }

        if (_gpsStore)
        {
            _gpsStore.Value = new GpsCoordinate 
            { 
                Latitude = 37.774929f, 
                Longitude = -122.419416f 
            };
            LogTest($"GPS initialized: {_gpsStore.Value}");
        }
    }

    private void RunCyclicTest()
    {
        Debug.Log($"=== Format String Test Cycle {_testCounter} ===");
        
        switch (_testCounter % 5)
        {
            case 1:
                TestFloatFormatting();
                break;
            case 2:
                TestStringFormatting();
                break;
            case 3:
                TestIntFormatting();
                break;
            case 4:
                TestVector3Formatting();
                break;
            case 0:
                TestGpsFormatting();
                break;
        }
    }

    private void TestFloatFormatting()
    {
        if (!_floatStore) return;

        float newValue = Random.Range(0f, 1000f) + Random.Range(0f, 1f);
        _floatStore.Value = newValue;
        
        LogTest($"FLOAT FORMATTING TEST:");
        LogTest($"  Raw value: {newValue}");
        LogTest($"  Empty format string should show: {newValue}");
        LogTest($"  Format '{0:F2}' should show: {newValue:F2}");
        LogTest($"  Format 'Value: {0}' should show: Value: {newValue}");
    }

    private void TestStringFormatting()
    {
        if (!_stringStore) return;

        string[] testStrings = { "Hello", "World", "Unity", "Format", "Test" };
        string newValue = testStrings[Random.Range(0, testStrings.Length)] + " " + Random.Range(1, 100);
        _stringStore.Value = newValue;
        
        LogTest($"STRING FORMATTING TEST:");
        LogTest($"  Raw value: '{newValue}'");
        LogTest($"  Empty format string should show: {newValue}");
        LogTest($"  Format 'Text: {0}' should show: Text: {newValue}");
        LogTest($"  Format '[{0}]' should show: [{newValue}]");
    }

    private void TestIntFormatting()
    {
        if (!_intStore) return;

        int newValue = Random.Range(1, 100000);
        _intStore.Value = newValue;
        
        LogTest($"INT FORMATTING TEST:");
        LogTest($"  Raw value: {newValue}");
        LogTest($"  Empty format string should show: {newValue}");
        LogTest($"  Format '{0:N0}' should show: {newValue:N0}");
        LogTest($"  Format 'Count: {0}' should show: Count: {newValue}");
    }

    private void TestVector3Formatting()
    {
        if (!_vector3Store) return;

        var newValue = new Vector3(
            Random.Range(-10f, 10f),
            Random.Range(-10f, 10f),
            Random.Range(-10f, 10f)
        );
        _vector3Store.Value = newValue;
        
        LogTest($"VECTOR3 FORMATTING TEST:");
        LogTest($"  Raw value: {newValue}");
        LogTest($"  Empty format string should show: {newValue}");
        LogTest($"  Format 'Position: {0}' should show: Position: {newValue}");
        LogTest($"  Format '({0})' should show: ({newValue})");
    }

    private void TestGpsFormatting()
    {
        if (!_gpsStore) return;

        var newValue = new GpsCoordinate
        {
            Latitude = Random.Range(-90f, 90f),
            Longitude = Random.Range(-180f, 180f)
        };
        _gpsStore.Value = newValue;
        
        LogTest($"GPS FORMATTING TEST:");
        LogTest($"  Raw value: {newValue}");
        LogTest($"  Empty format string should show: {newValue}");
        LogTest($"  Format 'GPS: {0}' should show: GPS: {newValue}");
        LogTest($"  Format 'Coordinates: {0}' should show: Coordinates: {newValue}");
    }

    // Test null value handling
    public void TestNullValueHandling()
    {
        if (!_stringStore) return;

        LogTest("TESTING NULL VALUE HANDLING:");
        
        // Set string store to null (this might not work depending on ValueStore implementation)
        // This is more of a conceptual test
        LogTest("  Null values should display as 'null'");
        LogTest("  This applies regardless of format string setting");
    }

    // Manual test methods for UI buttons
    public void TestFloatManual()
    {
        TestFloatFormatting();
    }

    public void TestStringManual()
    {
        TestStringFormatting();
    }

    public void TestIntManual()
    {
        TestIntFormatting();
    }

    public void TestVector3Manual()
    {
        TestVector3Formatting();
    }

    public void TestGpsManual()
    {
        TestGpsFormatting();
    }

    public void RunAllTests()
    {
        TestFloatFormatting();
        TestStringFormatting();
        TestIntFormatting();
        TestVector3Formatting();
        TestGpsFormatting();
        TestNullValueHandling();
    }

    private void LogTest(string message)
    {
        if (_logDetailedInfo)
        {
            Debug.Log($"[FormatTest] {message}");
        }
    }

    private void OnGUI()
    {
        if (!_enableAutoTest) return;

        GUILayout.BeginArea(new Rect(10, 10, 400, 350));
        GUILayout.Label("Format String Handling Test", GUI.skin.box);
        
        GUILayout.Label($"Test Cycle: {_testCounter}");
        GUILayout.Label($"Next test in: {(_testInterval - (Time.time - _lastTestTime)):F1}s");
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Run All Format Tests"))
        {
            RunAllTests();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Individual Tests:", GUI.skin.box);
        
        if (GUILayout.Button("Test Float Formatting"))
        {
            TestFloatManual();
        }
        
        if (GUILayout.Button("Test String Formatting"))
        {
            TestStringManual();
        }
        
        if (GUILayout.Button("Test Int Formatting"))
        {
            TestIntManual();
        }
        
        if (GUILayout.Button("Test Vector3 Formatting"))
        {
            TestVector3Manual();
        }
        
        if (GUILayout.Button("Test GPS Formatting"))
        {
            TestGpsManual();
        }
        
        if (GUILayout.Button("Test Null Handling"))
        {
            TestNullValueHandling();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Setup Instructions:", GUI.skin.box);
        GUILayout.Label("1. Create GenericPropertyBinding components");
        GUILayout.Label("2. Test with different format strings:");
        GUILayout.Label("   • Empty = raw ToString()");
        GUILayout.Label("   • '{0:F2}' = formatted");
        GUILayout.Label("   • 'Label: {0}' = with label");
        
        GUILayout.EndArea();
    }
}
