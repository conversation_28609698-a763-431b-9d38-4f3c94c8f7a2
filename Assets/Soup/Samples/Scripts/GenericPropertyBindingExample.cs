using Soup;
using UnityEngine;

/// <summary>
/// Example script demonstrating the GenericPropertyBinding component.
/// This script shows how to use the generic binding to display specific properties
/// from complex ValueStore types like GpsCoordinate and PlayerState.
/// </summary>
public class GenericPropertyBindingExample : MonoBehaviour
{
    [Header("Value Stores")]
    [SerializeField] private GpsCoordinateStore _gpsCoordinateStore;
    [SerializeField] private PlayerStateStore _playerStateStore;
    [SerializeField] private FloatStore _floatStore;
    [SerializeField] private Vector3Store _vector3Store;

    [Header("Test Controls")]
    [SerializeField] private bool _runTests = false;

    private void Start()
    {
        if (_runTests)
        {
            RunExamples();
        }
    }

    private void Update()
    {
        // Simple test controls for runtime testing
        if (Input.GetKeyDown(KeyCode.Alpha1))
        {
            TestGpsCoordinateUpdates();
        }
        
        if (Input.GetKeyDown(KeyCode.Alpha2))
        {
            TestPlayerStateUpdates();
        }
        
        if (Input.GetKeyDown(KeyCode.Alpha3))
        {
            TestPrimitiveUpdates();
        }
    }

    private void RunExamples()
    {
        Debug.Log("=== GenericPropertyBinding Examples ===");
        
        // Initialize some test data
        if (_gpsCoordinateStore)
        {
            _gpsCoordinateStore.Value = new GpsCoordinate 
            { 
                Latitude = 37.7749f, 
                Longitude = -122.4194f 
            };
            Debug.Log($"GPS Coordinate initialized: {_gpsCoordinateStore.Value}");
        }

        if (_playerStateStore)
        {
            _playerStateStore.Value = new PlayerState 
            { 
                Level = 1, 
                Position = Vector3.zero 
            };
            Debug.Log($"Player State initialized: Level={_playerStateStore.Value.Level}, Position={_playerStateStore.Value.Position}");
        }

        if (_floatStore)
        {
            _floatStore.Value = 42.5f;
            Debug.Log($"Float Store initialized: {_floatStore.Value}");
        }

        if (_vector3Store)
        {
            _vector3Store.Value = new Vector3(1, 2, 3);
            Debug.Log($"Vector3 Store initialized: {_vector3Store.Value}");
        }
    }

    private void TestGpsCoordinateUpdates()
    {
        if (!_gpsCoordinateStore) return;

        // Update GPS coordinates - this should trigger any GenericPropertyBinding 
        // components that are bound to "Latitude" or "Longitude" properties
        var newCoordinate = new GpsCoordinate
        {
            Latitude = Random.Range(-90f, 90f),
            Longitude = Random.Range(-180f, 180f)
        };
        
        _gpsCoordinateStore.Value = newCoordinate;
        Debug.Log($"GPS Updated: Lat={newCoordinate.Latitude:F2}, Lon={newCoordinate.Longitude:F2}");
    }

    private void TestPlayerStateUpdates()
    {
        if (!_playerStateStore) return;

        // Update player state - this should trigger any GenericPropertyBinding
        // components that are bound to "Level" or "Position" properties
        var newState = new PlayerState
        {
            Level = Random.Range(1, 100),
            Position = new Vector3(
                Random.Range(-10f, 10f),
                Random.Range(0f, 5f),
                Random.Range(-10f, 10f)
            )
        };
        
        _playerStateStore.Value = newState;
        Debug.Log($"Player Updated: Level={newState.Level}, Position={newState.Position}");
    }

    private void TestPrimitiveUpdates()
    {
        if (_floatStore)
        {
            _floatStore.Value = Random.Range(0f, 100f);
            Debug.Log($"Float Updated: {_floatStore.Value:F2}");
        }

        if (_vector3Store)
        {
            _vector3Store.Value = new Vector3(
                Random.Range(-5f, 5f),
                Random.Range(-5f, 5f),
                Random.Range(-5f, 5f)
            );
            Debug.Log($"Vector3 Updated: {_vector3Store.Value}");
        }
    }

    private void OnGUI()
    {
        if (!_runTests) return;

        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("GenericPropertyBinding Test Controls", GUI.skin.box);
        
        if (GUILayout.Button("Update GPS Coordinates (Key: 1)"))
        {
            TestGpsCoordinateUpdates();
        }
        
        if (GUILayout.Button("Update Player State (Key: 2)"))
        {
            TestPlayerStateUpdates();
        }
        
        if (GUILayout.Button("Update Primitives (Key: 3)"))
        {
            TestPrimitiveUpdates();
        }

        GUILayout.Space(10);
        GUILayout.Label("Instructions:", GUI.skin.box);
        GUILayout.Label("1. Create TextMeshPro objects in scene");
        GUILayout.Label("2. Add GenericPropertyBinding components");
        GUILayout.Label("3. Assign ValueStores and property names");
        GUILayout.Label("4. Use buttons or keys to test updates");
        
        GUILayout.EndArea();
    }
}
