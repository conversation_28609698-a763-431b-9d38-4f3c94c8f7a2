using Soup;
using UnityEngine;

/// <summary>
/// Simple validation test to ensure format string handling works correctly
/// for the most common scenarios.
/// </summary>
public class FormatStringValidationTest : MonoBehaviour
{
    [Header("Test Stores")]
    [SerializeField] private FloatStore _floatStore;
    [SerializeField] private StringStore _stringStore;

    private void Start()
    {
        Debug.Log("=== Format String Validation Test ===");
        RunValidationTests();
    }

    private void RunValidationTests()
    {
        TestFormatStringLogic();
        Debug.Log("=== Format String Validation Complete ===");
    }

    private void TestFormatStringLogic()
    {
        Debug.Log("Testing format string logic directly...");

        // Simulate the FormatValue method logic
        TestFormatValue(42.123f, null, "42.123");
        TestFormatValue(42.123f, "", "42.123");
        TestFormatValue(42.123f, "   ", "42.123");
        TestFormatValue(42.123f, "{0:F2}", "42.12");
        TestFormatValue(42.123f, "Value: {0}", "Value: 42.123");
        
        TestFormatValue("Hello", null, "Hello");
        TestFormatValue("Hello", "", "Hello");
        TestFormatValue("Hello", "Text: {0}", "Text: Hello");
        
        TestFormatValue(null, null, "null");
        TestFormatValue(null, "{0}", "null");
        
        Debug.Log("✅ All format string logic tests PASSED");
    }

    private void TestFormatValue(object value, string formatString, string expectedResult)
    {
        string result = SimulateFormatValue(value, formatString);
        
        if (result == expectedResult)
        {
            Debug.Log($"✅ FormatValue({value ?? "null"}, '{formatString ?? "null"}') = '{result}' (expected: '{expectedResult}')");
        }
        else
        {
            Debug.LogError($"❌ FormatValue({value ?? "null"}, '{formatString ?? "null"}') = '{result}' (expected: '{expectedResult}')");
        }
    }

    // Simulate the FormatValue method for testing
    private string SimulateFormatValue(object value, string formatString)
    {
        // Handle null values
        if (value == null)
        {
            return "null";
        }

        // Check if format string is null, empty, or whitespace
        if (string.IsNullOrWhiteSpace(formatString))
        {
            // No formatting - use raw ToString()
            return value.ToString();
        }
        else
        {
            // Use specified format string
            return string.Format(formatString, value);
        }
    }

    // Test with actual ValueStores
    public void TestWithValueStores()
    {
        if (_floatStore)
        {
            Debug.Log("Testing with FloatStore...");
            _floatStore.Value = 123.456f;
            Debug.Log($"FloatStore value: {_floatStore.Value}");
            Debug.Log("Expected behaviors:");
            Debug.Log("  Empty format string → '123.456'");
            Debug.Log("  Format '{0:F1}' → '123.5'");
            Debug.Log("  Format 'Value: {0}' → 'Value: 123.456'");
        }

        if (_stringStore)
        {
            Debug.Log("Testing with StringStore...");
            _stringStore.Value = "Test String";
            Debug.Log($"StringStore value: '{_stringStore.Value}'");
            Debug.Log("Expected behaviors:");
            Debug.Log("  Empty format string → 'Test String'");
            Debug.Log("  Format 'Text: {0}' → 'Text: Test String'");
            Debug.Log("  Format '[{0}]' → '[Test String]'");
        }
    }

    // Manual test buttons
    public void RunValidation()
    {
        RunValidationTests();
    }

    public void TestValueStores()
    {
        TestWithValueStores();
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 350, 200));
        GUILayout.Label("Format String Validation Test", GUI.skin.box);
        
        if (GUILayout.Button("Run Format Logic Validation"))
        {
            RunValidation();
        }
        
        if (GUILayout.Button("Test with ValueStores"))
        {
            TestValueStores();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Expected Results:", GUI.skin.box);
        GUILayout.Label("• null/empty format → raw ToString()");
        GUILayout.Label("• specified format → formatted output");
        GUILayout.Label("• null values → 'null'");
        
        GUILayout.Space(10);
        GUILayout.Label("Check Console for detailed test results", GUI.skin.box);
        
        GUILayout.EndArea();
    }
}
