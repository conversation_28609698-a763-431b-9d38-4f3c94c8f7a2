using System;
using System.Linq;
using Soup;
using UnityEngine;

public class SoupExamples : MonoBehaviour
{
    [SerializeField] private GpsCoordinateStore _gpsCoordinateStore;
    [SerializeField] private IntStore _intVar;
    [SerializeField] private GameObjectCollection _collection;
    [SerializeField] private ComponentCollection _componentCollection;
    [SerializeField] private IntEvent _intEvent;
    [SerializeField] private StringStore _stringStore;
    [SerializeField] private PlayerStateStore _playerStateStore;

    private void Start()
    {
        CollectionExample();
        EventExample();
        StoreExample();
    }

    private void EventExample()
    {
        // Will be visible in the event editor as a listener.
        _intEvent.AddListener(Print);

        // Will NOT be visible in the event editor as a listener.
        // Anonymous lambdas defined within a method don't inherently belong to a specific object instance
        // unless they capture it in some way.
        _intEvent.AddListener(value => print(value));

        // Will be visible in the event editor as a listener.
        _intEvent.AddListener(value =>
        {
            // Capture this explicitly.
            _ = this;
            print(value);
        });
    }

    public void Print(int value) => print(value);

    private void StoreExample()
    {
        _gpsCoordinateStore.AddListener(newValue =>
        {
            _ = this;
            print($"New value: {newValue}");
        });

        _gpsCoordinateStore.AddListener(
            value => value.Latitude,
            newValue => print($"Selective new value: {newValue}")
        );

        // Should not trigger the selective listener, as the Latitude prop has not changed.
        _gpsCoordinateStore.Value = new GpsCoordinate { Latitude = 5, Longitude = 50 };

        // Should trigger both listeners.
        _gpsCoordinateStore.Value = new GpsCoordinate { Latitude = 66, Longitude = 50 };

        _stringStore.Value = "Bar";
        _playerStateStore.Value = new PlayerState { Level = 10 };
    }

    private void CollectionExample()
    {
        for (int i = 0; i < 100; i++)
        {
            GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
            cube.name = i.ToString();
            _collection.Add(cube);
        }

        _collection.OnItemsRemoved.AddListener(items =>
        {
            for (int i = items.Length - 1; i >= 0; i--)
            {
                Destroy(items[i]);
            }
        });
        _collection.RemoveAll(x => Convert.ToInt32(x.name) % 3 == 0);

        // Works with LINQ statements
        _componentCollection.AddRange(_collection.Select(x => x.GetComponent<Collider>()));
    }
}