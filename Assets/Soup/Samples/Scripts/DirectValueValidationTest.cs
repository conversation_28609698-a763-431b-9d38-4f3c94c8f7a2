using Soup;
using UnityEngine;

/// <summary>
/// Simple validation test to ensure direct value binding works correctly
/// for the most common scenarios.
/// </summary>
public class DirectValueValidationTest : MonoBehaviour
{
    [Header("Test Stores")]
    [SerializeField] private FloatStore _floatStore;
    [SerializeField] private StringStore _stringStore;
    [SerializeField] private GpsCoordinateStore _gpsStore;

    private void Start()
    {
        Debug.Log("=== Direct Value Binding Validation Test ===");
        RunValidationTests();
    }

    private void RunValidationTests()
    {
        TestFloatDirectBinding();
        TestStringDirectBinding();
        TestGpsDirectBinding();
        TestGpsMemberBinding();
        
        Debug.Log("=== Validation Test Complete ===");
    }

    private void TestFloatDirectBinding()
    {
        if (!_floatStore)
        {
            Debug.LogWarning("FloatStore not assigned - skipping test");
            return;
        }

        try
        {
            Debug.Log("Testing FloatStore direct binding...");
            
            // Test direct value subscription (empty property name equivalent)
            var subscription = _floatStore.AddListener(value => 
                Debug.Log($"✅ Float direct binding: {value:F2}")
            );

            // Update value to trigger subscription
            _floatStore.Value = 123.45f;
            
            subscription.Dispose();
            Debug.Log("✅ FloatStore direct binding test PASSED");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"❌ FloatStore direct binding test FAILED: {ex.Message}");
        }
    }

    private void TestStringDirectBinding()
    {
        if (!_stringStore)
        {
            Debug.LogWarning("StringStore not assigned - skipping test");
            return;
        }

        try
        {
            Debug.Log("Testing StringStore direct binding...");
            
            // Test direct value subscription
            var subscription = _stringStore.AddListener(value => 
                Debug.Log($"✅ String direct binding: '{value}'")
            );

            // Update value to trigger subscription
            _stringStore.Value = "Direct Binding Test";
            
            subscription.Dispose();
            Debug.Log("✅ StringStore direct binding test PASSED");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"❌ StringStore direct binding test FAILED: {ex.Message}");
        }
    }

    private void TestGpsDirectBinding()
    {
        if (!_gpsStore)
        {
            Debug.LogWarning("GpsCoordinateStore not assigned - skipping test");
            return;
        }

        try
        {
            Debug.Log("Testing GpsCoordinateStore direct binding...");
            
            // Test direct value subscription (entire struct)
            var subscription = _gpsStore.AddListener(value => 
                Debug.Log($"✅ GPS direct binding: {value}")
            );

            // Update value to trigger subscription
            _gpsStore.Value = new GpsCoordinate { Latitude = 40.7128f, Longitude = -74.0060f };
            
            subscription.Dispose();
            Debug.Log("✅ GpsCoordinateStore direct binding test PASSED");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"❌ GpsCoordinateStore direct binding test FAILED: {ex.Message}");
        }
    }

    private void TestGpsMemberBinding()
    {
        if (!_gpsStore)
        {
            Debug.LogWarning("GpsCoordinateStore not assigned - skipping member binding test");
            return;
        }

        try
        {
            Debug.Log("Testing GpsCoordinateStore member binding (for comparison)...");
            
            // Test member binding (specific field)
            var subscription = _gpsStore.AddListener(
                gps => gps.Latitude,  // Member selector
                lat => Debug.Log($"✅ GPS member binding (Latitude): {lat:F4}")
            );

            // Update value to trigger subscription
            var currentGps = _gpsStore.Value;
            currentGps.Latitude = 51.5074f; // London
            _gpsStore.Value = currentGps;
            
            subscription.Dispose();
            Debug.Log("✅ GpsCoordinateStore member binding test PASSED");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"❌ GpsCoordinateStore member binding test FAILED: {ex.Message}");
        }
    }

    // Manual test buttons
    public void TestFloat()
    {
        TestFloatDirectBinding();
    }

    public void TestString()
    {
        TestStringDirectBinding();
    }

    public void TestGpsDirect()
    {
        TestGpsDirectBinding();
    }

    public void TestGpsMember()
    {
        TestGpsMemberBinding();
    }

    public void RunAllTests()
    {
        RunValidationTests();
    }

    private void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Direct Value Binding Validation", GUI.skin.box);
        
        if (GUILayout.Button("Run All Validation Tests"))
        {
            RunAllTests();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Test Float Direct"))
        {
            TestFloat();
        }
        
        if (GUILayout.Button("Test String Direct"))
        {
            TestString();
        }
        
        if (GUILayout.Button("Test GPS Direct"))
        {
            TestGpsDirect();
        }
        
        if (GUILayout.Button("Test GPS Member"))
        {
            TestGpsMember();
        }
        
        GUILayout.Space(10);
        GUILayout.Label("Check Console for test results", GUI.skin.box);
        
        GUILayout.EndArea();
    }
}
