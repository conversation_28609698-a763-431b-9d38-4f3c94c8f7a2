using System;
using System.Linq.Expressions;
using System.Reflection;
using TMPro;
using UnityEngine;

namespace Soup
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(TMP_Text))]
    [AddComponentMenu(Constants.BINDINGS_MENU_PATH + "Generic Property Binding")]
    [DisallowMultipleComponent]
    public class GenericPropertyBinding : MonoBehaviour
    {
        [SerializeField] private ValueStoreBase _valueStore;
        [SerializeField] private string _propertyName = "";
        [SerializeField] private string _formatString = "{0}";

        private TMP_Text _text;
        private EventSubscription _subscription;
        private MemberInfo _memberInfo;
        private bool _isValidBinding;
        private bool _isDirectValueBinding;

        private void Awake()
        {
            _text = GetComponent<TMP_Text>();
            ValidateBinding();
        }

        private void OnEnable()
        {
            if (!_isValidBinding) return;

            UpdateDisplayValue();
            SubscribeToChanges();
        }

        private void OnDisable()
        {
            _subscription?.Dispose();
        }

        private void ValidateBinding()
        {
            ResetBindingState();

            if (!_valueStore)
            {
                SetErrorText("No ValueStore assigned");
                return;
            }

            if (_valueStore.GetValue() == null)
            {
                SetErrorText("ValueStore.Value is null");
                return;
            }

            if (string.IsNullOrEmpty(_propertyName))
            {
                _isDirectValueBinding = true;
                _isValidBinding = true;
                return;
            }

            Type valueType = _valueStore.ValueType;
            _memberInfo = valueType.GetProperty(_propertyName, BindingFlags.Public | BindingFlags.Instance) as MemberInfo
                         ?? valueType.GetField(_propertyName, BindingFlags.Public | BindingFlags.Instance);

            if (_memberInfo == null)
            {
                SetErrorText($"Property or field '{_propertyName}' not found on type {valueType.Name}");
                return;
            }

            if (_memberInfo is PropertyInfo prop && !prop.CanRead)
            {
                SetErrorText($"Property '{_propertyName}' is not readable");
                return;
            }

            _isValidBinding = true;
        }

        private void ResetBindingState()
        {
            _isValidBinding = false;
            _memberInfo = null;
            _isDirectValueBinding = false;
        }

        private void SubscribeToChanges()
        {
            if (!_isValidBinding) return;

            try
            {
                Type valueType = _valueStore.ValueType;
                Type targetType = GetTargetType();

                ParameterExpression param = Expression.Parameter(valueType, "value");
                Expression selectorExpression = _isDirectValueBinding
                    ? param
                    : CreateMemberExpression(param);

                Type selectorType = typeof(Func<,>).MakeGenericType(valueType, targetType);
                LambdaExpression lambda = Expression.Lambda(selectorType, selectorExpression, param);
                Delegate selector = lambda.Compile();

                Delegate listener = CreateGenericListener(targetType);
                _subscription = _valueStore.AddListenerWithSelector(selector, listener);
            }
            catch (Exception ex)
            {
                SetErrorText($"Error subscribing to changes: {ex.Message}");
            }
        }

        private Type GetTargetType()
        {
            if (_isDirectValueBinding) return _valueStore.ValueType;
            return _memberInfo is PropertyInfo prop ? prop.PropertyType : ((FieldInfo)_memberInfo).FieldType;
        }

        private Expression CreateMemberExpression(ParameterExpression param)
        {
            return _memberInfo is PropertyInfo prop
                ? Expression.Property(param, prop)
                : Expression.Field(param, (FieldInfo)_memberInfo);
        }

        private Delegate CreateGenericListener(Type memberType)
        {
            Type actionType = typeof(Action<>).MakeGenericType(memberType);
            MethodInfo updateMethod = typeof(GenericPropertyBinding).GetMethod(nameof(OnValueChanged), BindingFlags.NonPublic | BindingFlags.Instance);
            MethodInfo genericUpdateMethod = updateMethod.MakeGenericMethod(memberType);
            return Delegate.CreateDelegate(actionType, this, genericUpdateMethod);
        }

        private void OnValueChanged<T>(T newValue)
        {
            try
            {
                _text.text = FormatValue(newValue);
            }
            catch (Exception ex)
            {
                SetErrorText($"Error formatting value: {ex.Message}");
            }
        }

        private string FormatValue(object value)
        {
            if (value == null) return "null";

            return string.IsNullOrWhiteSpace(_formatString)
                ? value.ToString()
                : string.Format(_formatString, value);
        }

        private void UpdateDisplayValue()
        {
            if (!_isValidBinding) return;

            try
            {
                object storeValue = _valueStore.GetValue();
                object displayValue = GetDisplayValue(storeValue);
                _text.text = FormatValue(displayValue);
            }
            catch (Exception ex)
            {
                SetErrorText($"Error getting value: {ex.Message}");
            }
        }

        private object GetDisplayValue(object storeValue)
        {
            if (storeValue == null) return null;
            if (_isDirectValueBinding) return storeValue;

            return _memberInfo is PropertyInfo prop
                ? prop.GetValue(storeValue)
                : ((FieldInfo)_memberInfo).GetValue(storeValue);
        }

        private void SetErrorText(string error)
        {
            if (_text)
            {
                _text.text = $"[Error: {error}]";
            }
            Debug.LogWarning($"GenericPropertyBinding Error: {error}", this);
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (!_text)
                _text = GetComponent<TMP_Text>();

            ValidateBinding();
            
            if (_isValidBinding)
            {
                UpdateDisplayValue();
            }
        }
#endif
    }
}
