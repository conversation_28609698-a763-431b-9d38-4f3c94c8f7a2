using UnityEngine;
using UnityEngine.UI;

namespace Soup
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(Graphic))]
    [AddComponentMenu(Constants.BINDINGS_MENU_PATH + "Graphic - Color Binding")]
    [DisallowMultipleComponent]
    public class GraphicColorBinding : MonoBehaviour
    {
        [SerializeField]
        private ColorStore _colorStore;

        private Graphic _graphic;
        private EventSubscription _subscription;

        private void Awake()
        {
            _graphic = GetComponent<Graphic>();
        }

        private void OnEnable()
        {
            if (!_colorStore) return;

            _graphic.color = _colorStore.Value;
            _subscription = _colorStore.AddListener(OnColorChanged);
        }

        private void OnDisable()
        {
            _subscription?.Dispose();
        }

        private void OnColorChanged(Color color)
        {
            _graphic.color = color;
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (!_colorStore) return;

            if (!_graphic)
                _graphic = GetComponent<Graphic>();

            _graphic.color = _colorStore.Value;
        }
#endif
    }
}