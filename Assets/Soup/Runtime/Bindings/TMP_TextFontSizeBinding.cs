using TMPro;
using UnityEngine;

namespace Soup
{
    [ExecuteInEditMode]
    [RequireComponent(typeof(TMP_Text))]
    [AddComponentMenu(Constants.BINDINGS_MENU_PATH + "TextMeshPro - Font Size Binding")]
    [DisallowMultipleComponent]
    public class TMP_TextFontSizeBinding : MonoBehaviour
    {
        [SerializeField]
        private FloatStore _fontSizeStore;

        private TMP_Text _text;
        private EventSubscription _subscription;

        private void Awake()
        {
            _text = GetComponent<TMP_Text>();
        }

        private void OnEnable()
        {
            if (!_fontSizeStore) return;

            _text.fontSize = _fontSizeStore.Value;
            _subscription = _fontSizeStore.AddListener(OnFontSizeChanged);
        }

        private void OnDisable()
        {
            _subscription?.Dispose();
        }

        private void OnFontSizeChanged(float fontSize)
        {
            _text.fontSize = fontSize;
        }

#if UNITY_EDITOR
        private void OnValidate()
        {
            if (!_fontSizeStore) return;

            if (!_text)
                _text = GetComponent<TMP_Text>();

            _text.fontSize = _fontSizeStore.Value;
        }
#endif
    }
}