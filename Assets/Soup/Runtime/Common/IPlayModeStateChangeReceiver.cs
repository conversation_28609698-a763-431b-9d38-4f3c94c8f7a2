namespace Soup
{
    /// <summary>
    /// EDITOR MODE ONLY.
    /// Interface for objects that need to respond to Unity's play mode state changes.
    /// </summary>
    public interface IPlayModeStateChangeReceiver
    {
        /// <summary>
        /// Called when Unity Editor enters play mode.
        /// </summary>
        void OnWillEnterPlayMode();

        /// <summary>
        /// Called when Unity Editor is about to exit play mode.
        /// </summary>
        void OnExitingPlayMode();
    }
}