using System;

namespace Soup
{
    /// <summary>
    /// Interface for components that need dynamic ValueStore binding capabilities.
    /// This approach separates the concern of dynamic binding from the core ValueStore API.
    /// 
    /// This demonstrates Approach 3: Interface-Based Dynamic Binding
    /// </summary>
    public interface IDynamicValueStoreBinder
    {
        /// <summary>
        /// Dynamically binds to a ValueStore using reflection-based selectors.
        /// </summary>
        /// <param name="valueStore">The ValueStore to bind to</param>
        /// <param name="selector">Delegate that selects a property/field from the ValueStore's value</param>
        /// <param name="listener">Delegate that handles changes to the selected property/field</param>
        /// <returns>EventSubscription that can be disposed to unsubscribe</returns>
        EventSubscription BindToValueStore(ValueStoreBase valueStore, Delegate selector, Delegate listener);
    }

    /// <summary>
    /// Default implementation of IDynamicValueStoreBinder that uses reflection.
    /// Components can use this implementation or create their own optimized versions.
    /// </summary>
    public class ReflectionBasedValueStoreBinder : IDynamicValueStoreBinder
    {
        public EventSubscription BindToValueStore(ValueStoreBase valueStore, Delegate selector, Delegate listener)
        {
            // This would contain the same logic as ValueStoreBindingHelper.AddListenerWithSelector
            return ValueStoreBindingHelper.AddListenerWithSelector(valueStore, selector, listener);
        }
    }

    /// <summary>
    /// Extension methods to make the interface easier to use.
    /// </summary>
    public static class DynamicValueStoreBinderExtensions
    {
        /// <summary>
        /// Convenience method for binding to ValueStore properties/fields.
        /// </summary>
        public static EventSubscription BindToProperty<T, TProperty>(
            this IDynamicValueStoreBinder binder,
            ValueStore<T> valueStore,
            Func<T, TProperty> selector,
            Action<TProperty> listener)
        {
            return binder.BindToValueStore(valueStore, selector, listener);
        }
    }
}
