using System;
using UnityEngine;

namespace Soup
{
    /// <summary>
    /// Test script to verify that the ValueStoreBindingHelper works correctly
    /// and provides the same functionality as the removed AddListenerWithSelector method.
    /// </summary>
    public class ValueStoreBindingHelper_Test : MonoBehaviour
    {
        [Header("Test Value Stores")]
        [SerializeField] private GpsCoordinateStore _gpsStore;
        [SerializeField] private Vector3Store _vector3Store;
        [SerializeField] private FloatStore _floatStore;

        [Header("Test Configuration")]
        [SerializeField] private bool _runTestsOnStart = true;
        [SerializeField] private bool _enableDetailedLogging = true;

        private void Start()
        {
            if (_runTestsOnStart)
            {
                Debug.Log("=== ValueStoreBindingHelper Test ===");
                Debug.Log("Testing the new helper class that replaced AddListenerWithSelector");
                
                InitializeTestData();
                RunAllTests();
            }
        }

        private void InitializeTestData()
        {
            if (_gpsStore)
            {
                _gpsStore.Value = new GpsCoordinate { Latitude = 37.7749f, Longitude = -122.4194f };
                LogTest("GPS Store initialized");
            }

            if (_vector3Store)
            {
                _vector3Store.Value = new Vector3(1f, 2f, 3f);
                LogTest("Vector3 Store initialized");
            }

            if (_floatStore)
            {
                _floatStore.Value = 42.5f;
                LogTest("Float Store initialized");
            }
        }

        private void RunAllTests()
        {
            LogTest("Starting ValueStoreBindingHelper tests...");

            // Test 1: GPS Coordinate field binding
            TestGpsFieldBinding();

            // Test 2: Vector3 property binding
            TestVector3PropertyBinding();

            // Test 3: Direct value binding
            TestDirectValueBinding();

            // Test 4: Error handling
            TestErrorHandling();

            LogTest("All ValueStoreBindingHelper tests completed!");
        }

        private void TestGpsFieldBinding()
        {
            if (!_gpsStore)
            {
                LogError("GpsCoordinateStore not assigned - skipping field binding test");
                return;
            }

            try
            {
                LogTest("Testing GPS Coordinate field binding...");

                // Create selector and listener delegates
                Func<GpsCoordinate, float> selector = gps => gps.Latitude;
                Action<float> listener = lat => LogTest($"GPS Latitude changed to: {lat:F4}");

                // Use the new helper method instead of the old base class method
                var subscription = ValueStoreBindingHelper.AddListenerWithSelector(_gpsStore, selector, listener);

                // Test the subscription by changing the value
                var newGps = new GpsCoordinate { Latitude = 40.7128f, Longitude = _gpsStore.Value.Longitude };
                _gpsStore.Value = newGps;

                subscription.Dispose();
                LogTest("✅ GPS field binding test PASSED");
            }
            catch (Exception ex)
            {
                LogError($"❌ GPS field binding test FAILED: {ex.Message}");
            }
        }

        private void TestVector3PropertyBinding()
        {
            if (!_vector3Store)
            {
                LogError("Vector3Store not assigned - skipping property binding test");
                return;
            }

            try
            {
                LogTest("Testing Vector3 property binding...");

                // Create selector and listener delegates
                Func<Vector3, float> selector = vec => vec.magnitude;
                Action<float> listener = mag => LogTest($"Vector3 magnitude changed to: {mag:F3}");

                // Use the new helper method
                var subscription = ValueStoreBindingHelper.AddListenerWithSelector(_vector3Store, selector, listener);

                // Test the subscription by changing the value
                _vector3Store.Value = new Vector3(3f, 4f, 0f); // magnitude = 5

                subscription.Dispose();
                LogTest("✅ Vector3 property binding test PASSED");
            }
            catch (Exception ex)
            {
                LogError($"❌ Vector3 property binding test FAILED: {ex.Message}");
            }
        }

        private void TestDirectValueBinding()
        {
            if (!_floatStore)
            {
                LogError("FloatStore not assigned - skipping direct value binding test");
                return;
            }

            try
            {
                LogTest("Testing direct value binding...");

                // Create selector and listener delegates for direct value access
                Func<float, float> selector = value => value;
                Action<float> listener = val => LogTest($"Float value changed to: {val:F2}");

                // Use the new helper method
                var subscription = ValueStoreBindingHelper.AddListenerWithSelector(_floatStore, selector, listener);

                // Test the subscription by changing the value
                _floatStore.Value = 99.9f;

                subscription.Dispose();
                LogTest("✅ Direct value binding test PASSED");
            }
            catch (Exception ex)
            {
                LogError($"❌ Direct value binding test FAILED: {ex.Message}");
            }
        }

        private void TestErrorHandling()
        {
            LogTest("Testing error handling...");

            try
            {
                // Test with null ValueStore
                try
                {
                    Func<float, float> selector = value => value;
                    Action<float> listener = val => { };
                    ValueStoreBindingHelper.AddListenerWithSelector(null, selector, listener);
                    LogError("❌ Null ValueStore test FAILED - should have thrown exception");
                }
                catch (ArgumentNullException)
                {
                    LogTest("✅ Null ValueStore test PASSED - correctly threw ArgumentNullException");
                }

                // Test with mismatched delegate types
                if (_floatStore)
                {
                    try
                    {
                        Func<int, int> wrongSelector = value => value; // Wrong input type
                        Action<int> listener = val => { };
                        ValueStoreBindingHelper.AddListenerWithSelector(_floatStore, wrongSelector, listener);
                        LogError("❌ Type mismatch test FAILED - should have thrown exception");
                    }
                    catch (ArgumentException)
                    {
                        LogTest("✅ Type mismatch test PASSED - correctly threw ArgumentException");
                    }
                }

                LogTest("✅ Error handling tests PASSED");
            }
            catch (Exception ex)
            {
                LogError($"❌ Error handling tests FAILED: {ex.Message}");
            }
        }

        // Manual test methods for UI buttons
        public void TestGpsManual()
        {
            TestGpsFieldBinding();
        }

        public void TestVector3Manual()
        {
            TestVector3PropertyBinding();
        }

        public void TestFloatManual()
        {
            TestDirectValueBinding();
        }

        public void TestErrorsManual()
        {
            TestErrorHandling();
        }

        public void RunAllTestsManual()
        {
            RunAllTests();
        }

        private void LogTest(string message)
        {
            if (_enableDetailedLogging)
            {
                Debug.Log($"[BindingHelperTest] {message}");
            }
        }

        private void LogError(string message)
        {
            Debug.LogError($"[BindingHelperTest] {message}");
        }

        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.Label("ValueStoreBindingHelper Test", GUI.skin.box);
            
            if (GUILayout.Button("Run All Tests"))
            {
                RunAllTestsManual();
            }
            
            GUILayout.Space(10);
            GUILayout.Label("Individual Tests:", GUI.skin.box);
            
            if (GUILayout.Button("Test GPS Field Binding"))
            {
                TestGpsManual();
            }
            
            if (GUILayout.Button("Test Vector3 Property Binding"))
            {
                TestVector3Manual();
            }
            
            if (GUILayout.Button("Test Direct Value Binding"))
            {
                TestFloatManual();
            }
            
            if (GUILayout.Button("Test Error Handling"))
            {
                TestErrorsManual();
            }
            
            GUILayout.Space(10);
            GUILayout.Label("This test verifies that ValueStoreBindingHelper", GUI.skin.box);
            GUILayout.Label("provides the same functionality as the removed");
            GUILayout.Label("AddListenerWithSelector method from ValueStoreBase.");
            
            GUILayout.EndArea();
        }
    }
}
