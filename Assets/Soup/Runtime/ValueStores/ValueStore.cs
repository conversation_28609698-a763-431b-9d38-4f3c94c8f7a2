using System;
using System.Linq;
using System.Reflection;
using UnityEngine;

namespace Soup
{
    /// <summary>
    /// Non-generic base class for all ValueStore types.
    /// Provides common functionality and allows for type-safe casting without reflection.
    /// </summary>
    [Serializable]
    public abstract class ValueStoreBase : ScriptableObject, IPlayModeStateChangeReceiver
    {
        [SerializeField]
        protected bool _resetAfterPlayModeExit = true;

        public abstract Type ValueType { get; }
        public abstract object GetValue();
        public abstract void SetValue(object value);
        public abstract EventSubscription AddListener(Delegate listener);
        public abstract EventSubscription AddListenerWithSelector(Delegate selector, Delegate listener);

        public virtual void Awake()
        {
            // Prevents from resetting if no reference in a scene
            hideFlags = HideFlags.DontUnloadUnusedAsset;
        }

        public abstract void OnWillEnterPlayMode();
        public abstract void OnExitingPlayMode();
    }

    [Serializable]
    public abstract class ValueStore<T> : ValueStoreBase
    {
        private readonly EventRegistry<T> _eventRegistry = new();

        [SerializeField]
        private T _value;

        public T Value
        {
            get => _value;
            set
            {
                if (Value.Equals(value)) return;

                _value = value;
                NotifyChange();
            }
        }

        private T _initialValue;

        // Base class implementations
        public override Type ValueType => typeof(T);
        public override object GetValue() => _value;
        public override void SetValue(object value) => Value = (T)value;
        public override EventSubscription AddListener(Delegate listener) => AddListener((Action<T>)listener);
        public override EventSubscription AddListenerWithSelector(Delegate selector, Delegate listener)
        {
            var selectorType = selector.GetType();
            var listenerType = listener.GetType();

            if (!IsValidDelegateTypes(selectorType, listenerType, out var selectionType))
            {
                throw new ArgumentException($"Invalid delegate types: selector={selectorType.Name}, listener={listenerType.Name}");
            }

            var genericMethod = GetType().GetMethod("AddListener", new[] { selectorType, listenerType });
            if (genericMethod == null)
            {
                genericMethod = GetType().GetMethods(BindingFlags.Public | BindingFlags.Instance)
                    .First(m => m.Name == "AddListener" && m.IsGenericMethodDefinition && m.GetParameters().Length == 2)
                    .MakeGenericMethod(selectionType);
            }

            return (EventSubscription)genericMethod.Invoke(this, new object[] { selector, listener });
        }

        private bool IsValidDelegateTypes(Type selectorType, Type listenerType, out Type selectionType)
        {
            selectionType = null;

            if (!selectorType.IsGenericType || selectorType.GetGenericTypeDefinition() != typeof(Func<,>) ||
                !listenerType.IsGenericType || listenerType.GetGenericTypeDefinition() != typeof(Action<>))
            {
                return false;
            }

            var valueType = selectorType.GetGenericArguments()[0];
            selectionType = selectorType.GetGenericArguments()[1];
            var actionType = listenerType.GetGenericArguments()[0];

            return valueType == typeof(T) && selectionType == actionType;
        }

        public EventSubscription AddListener(Action<T> listener) => _eventRegistry.AddListener(listener);

        public EventSubscription AddListener<TSelection>(Func<T, TSelection> selector, Action<TSelection> listener)
        {
            var currentValue = selector(Value);

            void Action(T _)
            {
                var newValue = selector(Value);

                if (!currentValue.Equals(newValue))
                {
                    currentValue = newValue;
                    listener?.Invoke(newValue);
                }
            }

            Action(Value);

            return _eventRegistry.AddListener(Action);
        }

        public void RemoveAllListeners() => _eventRegistry.Clear();

        public void NotifyChange() => _eventRegistry.Invoke(Value);

        public override void OnWillEnterPlayMode()
        {
            _initialValue = Value;
        }

        public override void OnExitingPlayMode()
        {
            // We always clear the collection and related event registries upon exiting play mode
            // to avoid keeping ghost objects and leak memory.
            RemoveAllListeners();

            if (_resetAfterPlayModeExit)
                Value = _initialValue;
        }

        private void OnValidate()
        {
            NotifyChange();
        }

        public override string ToString() => Value.ToString();
    }
}